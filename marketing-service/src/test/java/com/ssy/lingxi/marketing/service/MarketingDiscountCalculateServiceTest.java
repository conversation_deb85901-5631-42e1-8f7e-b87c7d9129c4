package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.marketing.model.req.MarketingDiscountCalculateReq;
import com.ssy.lingxi.marketing.model.resp.MarketingDiscountCalculateResp;
import com.ssy.lingxi.marketing.strategy.ActivityDiscountStrategy;
import com.ssy.lingxi.marketing.strategy.impl.FullAmountDiscountStrategy;
import com.ssy.lingxi.marketing.strategy.impl.FullQuantityDiscountStrategy;
import com.ssy.lingxi.marketing.strategy.impl.LaborDiscountStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 营销活动折扣计算服务测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-26
 */
@ExtendWith(MockitoExtension.class)
class MarketingDiscountCalculateServiceTest {

    @InjectMocks
    private MarketingDiscountCalculateService marketingDiscountCalculateService;

    private MarketingDiscountCalculateReq request;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        request = new MarketingDiscountCalculateReq();
        request.setShopId(123L);
        request.setMemberId(456L);
        request.setMemberRoleId(789L);
        request.setCalculateTime(LocalDateTime.now());

        // 准备商品列表
        MarketingDiscountCalculateReq.CommodityItem commodity1 = new MarketingDiscountCalculateReq.CommodityItem();
        commodity1.setCommoditySkuId(1001L);
        commodity1.setCommodityId(2001L);
        commodity1.setQuantity(2);
        commodity1.setUnitPrice(new BigDecimal("100.00"));
        commodity1.setTotalPrice(new BigDecimal("200.00"));
        commodity1.setCategoryId(3001L);
        commodity1.setCommodityName("黄金戒指");
        commodity1.setWeight(new BigDecimal("5.5"));

        MarketingDiscountCalculateReq.CommodityItem commodity2 = new MarketingDiscountCalculateReq.CommodityItem();
        commodity2.setCommoditySkuId(1002L);
        commodity2.setCommodityId(2002L);
        commodity2.setQuantity(1);
        commodity2.setUnitPrice(new BigDecimal("150.00"));
        commodity2.setTotalPrice(new BigDecimal("150.00"));
        commodity2.setCategoryId(3002L);
        commodity2.setCommodityName("黄金项链");
        commodity2.setWeight(new BigDecimal("8.0"));

        request.setCommodityList(Arrays.asList(commodity1, commodity2));
    }

    @Test
    void testCalculateMarketingActivityDiscount() {
        // 执行测试
        MarketingDiscountCalculateResp response = marketingDiscountCalculateService.calculateMarketingActivityDiscount(request);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getCommodityDiscountList());
        assertNotNull(response.getActivityDiscountList());
        assertEquals(2, response.getCommodityDiscountList().size());
        
        // 验证商品优惠详情
        for (MarketingDiscountCalculateResp.CommodityDiscountDetail detail : response.getCommodityDiscountList()) {
            assertNotNull(detail.getCommoditySkuId());
            assertNotNull(detail.getCommodityId());
            assertNotNull(detail.getActivityDiscount());
            assertNotNull(detail.getLaborDiscount());
            assertNotNull(detail.getTotalDiscount());
        }
    }

    /**
     * 测试工价优惠策略
     */
    @Test
    void testLaborDiscountStrategy() {
        LaborDiscountStrategy strategy = new LaborDiscountStrategy();
        
        // 准备活动信息
        ActivityDiscountStrategy.ActivityInfo activity = new ActivityDiscountStrategy.ActivityInfo();
        activity.setActivityId(5001L);
        activity.setActivityName("工价优惠活动");
        activity.setActivityType(17);
        activity.setLaborDiscountRate(new BigDecimal("0.1")); // 10%优惠

        // 测试条件检查
        boolean conditionMet = strategy.checkCondition(activity, request.getCommodityList());
        assertTrue(conditionMet);

        // 测试优惠计算
        BigDecimal discount = strategy.calculateDiscount(activity, request.getCommodityList());
        assertTrue(discount.compareTo(BigDecimal.ZERO) >= 0);

        // 测试优惠分摊
        List<ActivityDiscountStrategy.CommodityDiscountAllocation> allocations = 
                strategy.allocateDiscount(discount, request.getCommodityList());
        assertEquals(2, allocations.size());
    }

    /**
     * 测试满额促销策略
     */
    @Test
    void testFullAmountDiscountStrategy() {
        FullAmountDiscountStrategy strategy = new FullAmountDiscountStrategy();
        
        // 准备活动信息
        ActivityDiscountStrategy.ActivityInfo activity = new ActivityDiscountStrategy.ActivityInfo();
        activity.setActivityId(5002L);
        activity.setActivityName("满额促销活动");
        activity.setActivityType(18);
        activity.setFullAmountThreshold(new BigDecimal("300.00")); // 满300
        activity.setFullAmountDiscount(new BigDecimal("50.00"));   // 减50

        // 测试条件检查
        boolean conditionMet = strategy.checkCondition(activity, request.getCommodityList());
        assertTrue(conditionMet); // 总金额350，满足满300的条件

        // 测试优惠计算
        BigDecimal discount = strategy.calculateDiscount(activity, request.getCommodityList());
        assertEquals(new BigDecimal("50.00"), discount);

        // 测试优惠分摊
        List<ActivityDiscountStrategy.CommodityDiscountAllocation> allocations = 
                strategy.allocateDiscount(discount, request.getCommodityList());
        assertEquals(2, allocations.size());
        
        // 验证分摊金额总和等于总优惠
        BigDecimal totalAllocated = allocations.stream()
                .map(ActivityDiscountStrategy.CommodityDiscountAllocation::getDiscountAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        assertEquals(discount, totalAllocated);
    }

    /**
     * 测试满量促销策略
     */
    @Test
    void testFullQuantityDiscountStrategy() {
        FullQuantityDiscountStrategy strategy = new FullQuantityDiscountStrategy();
        
        // 准备活动信息
        ActivityDiscountStrategy.ActivityInfo activity = new ActivityDiscountStrategy.ActivityInfo();
        activity.setActivityId(5003L);
        activity.setActivityName("满量促销活动");
        activity.setActivityType(19);
        activity.setFullQuantityThreshold(3); // 满3件
        activity.setFullQuantityDiscount(new BigDecimal("30.00")); // 减30

        // 测试条件检查
        boolean conditionMet = strategy.checkCondition(activity, request.getCommodityList());
        assertTrue(conditionMet); // 总数量3，满足满3件的条件

        // 测试优惠计算
        BigDecimal discount = strategy.calculateDiscount(activity, request.getCommodityList());
        assertEquals(new BigDecimal("30.00"), discount);

        // 测试优惠分摊
        List<ActivityDiscountStrategy.CommodityDiscountAllocation> allocations = 
                strategy.allocateDiscount(discount, request.getCommodityList());
        assertEquals(2, allocations.size());
        
        // 验证分摊金额总和等于总优惠
        BigDecimal totalAllocated = allocations.stream()
                .map(ActivityDiscountStrategy.CommodityDiscountAllocation::getDiscountAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        assertEquals(discount, totalAllocated);
    }

    /**
     * 测试不满足条件的情况
     */
    @Test
    void testConditionNotMet() {
        FullAmountDiscountStrategy strategy = new FullAmountDiscountStrategy();
        
        // 准备活动信息 - 设置很高的门槛
        ActivityDiscountStrategy.ActivityInfo activity = new ActivityDiscountStrategy.ActivityInfo();
        activity.setActivityId(5004L);
        activity.setActivityName("高门槛满额促销");
        activity.setActivityType(18);
        activity.setFullAmountThreshold(new BigDecimal("1000.00")); // 满1000
        activity.setFullAmountDiscount(new BigDecimal("100.00"));   // 减100

        // 测试条件检查
        boolean conditionMet = strategy.checkCondition(activity, request.getCommodityList());
        assertFalse(conditionMet); // 总金额350，不满足满1000的条件

        // 测试优惠计算
        BigDecimal discount = strategy.calculateDiscount(activity, request.getCommodityList());
        assertEquals(BigDecimal.ZERO, discount); // 不满足条件，优惠为0
    }
}
