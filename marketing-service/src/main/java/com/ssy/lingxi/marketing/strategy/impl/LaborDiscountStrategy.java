package com.ssy.lingxi.marketing.strategy.impl;

import com.ssy.lingxi.marketing.model.req.MarketingDiscountCalculateReq;
import com.ssy.lingxi.marketing.strategy.ActivityDiscountStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 工价优惠活动策略（活动类型17）
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-26
 */
@Slf4j
@Component
public class LaborDiscountStrategy implements ActivityDiscountStrategy {

    private static final Integer ACTIVITY_TYPE = 17;

    @Override
    public Integer getSupportedActivityType() {
        return ACTIVITY_TYPE;
    }

    @Override
    public boolean checkCondition(ActivityInfo activity, List<MarketingDiscountCalculateReq.CommodityItem> commodityList) {
        log.info("检查工价优惠活动条件，活动ID: {}, 商品数量: {}", activity.getActivityId(), commodityList.size());
        
        // 工价优惠活动通常没有特殊条件，只要有商品参与即可
        boolean hasValidCommodity = commodityList != null && !commodityList.isEmpty();
        
        // 检查是否有工价优惠配置
        boolean hasLaborConfig = activity.getLaborDiscountRate() != null || activity.getLaborDiscountAmount() != null;
        
        log.info("工价优惠活动条件检查结果，活动ID: {}, 有效商品: {}, 有工价配置: {}", 
                activity.getActivityId(), hasValidCommodity, hasLaborConfig);
        
        return hasValidCommodity && hasLaborConfig;
    }

    @Override
    public BigDecimal calculateDiscount(ActivityInfo activity, List<MarketingDiscountCalculateReq.CommodityItem> commodityList) {
        log.info("计算工价优惠，活动ID: {}, 商品数量: {}", activity.getActivityId(), commodityList.size());
        
        BigDecimal totalDiscount = BigDecimal.ZERO;
        
        for (MarketingDiscountCalculateReq.CommodityItem commodity : commodityList) {
            BigDecimal commodityDiscount = calculateCommodityLaborDiscount(activity, commodity);
            totalDiscount = totalDiscount.add(commodityDiscount);
            
            log.debug("商品工价优惠计算，商品SKU: {}, 重量: {}, 优惠金额: {}", 
                    commodity.getCommoditySkuId(), commodity.getWeight(), commodityDiscount);
        }
        
        log.info("工价优惠总计算结果，活动ID: {}, 总优惠金额: {}", activity.getActivityId(), totalDiscount);
        return totalDiscount;
    }

    /**
     * 计算单个商品的工价优惠
     */
    private BigDecimal calculateCommodityLaborDiscount(ActivityInfo activity, MarketingDiscountCalculateReq.CommodityItem commodity) {
        // 如果设置了固定优惠金额，直接使用
        if (activity.getLaborDiscountAmount() != null && activity.getLaborDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
            return activity.getLaborDiscountAmount().multiply(new BigDecimal(commodity.getQuantity()));
        }
        
        // 如果设置了优惠比例，按比例计算
        if (activity.getLaborDiscountRate() != null && activity.getLaborDiscountRate().compareTo(BigDecimal.ZERO) > 0) {
            // 工价优惠通常基于商品重量计算
            BigDecimal weight = commodity.getWeight() != null ? commodity.getWeight() : BigDecimal.ZERO;
            BigDecimal baseAmount = weight.multiply(new BigDecimal(commodity.getQuantity()));
            return baseAmount.multiply(activity.getLaborDiscountRate()).setScale(2, RoundingMode.HALF_UP);
        }
        
        return BigDecimal.ZERO;
    }

    @Override
    public List<CommodityDiscountAllocation> allocateDiscount(BigDecimal totalDiscount, List<MarketingDiscountCalculateReq.CommodityItem> commodityList) {
        log.info("分摊工价优惠，总优惠金额: {}, 商品数量: {}", totalDiscount, commodityList.size());
        
        List<CommodityDiscountAllocation> allocations = new ArrayList<>();
        
        if (totalDiscount.compareTo(BigDecimal.ZERO) <= 0 || commodityList.isEmpty()) {
            return allocations;
        }
        
        // 工价优惠按商品实际计算的优惠金额分摊（已经在calculateDiscount中按商品计算了）
        // 这里重新计算每个商品的优惠金额
        for (MarketingDiscountCalculateReq.CommodityItem commodity : commodityList) {
            // 这里需要传入activity信息，但接口设计上没有，所以采用按比例分摊的方式
            // 实际项目中建议优化接口设计，将activity信息传入
            
            // 按商品总价比例分摊
            BigDecimal commodityTotalPrice = commodity.getTotalPrice();
            BigDecimal totalPrice = commodityList.stream()
                    .map(MarketingDiscountCalculateReq.CommodityItem::getTotalPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            if (totalPrice.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal ratio = commodityTotalPrice.divide(totalPrice, 6, RoundingMode.HALF_UP);
                BigDecimal allocation = totalDiscount.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
                
                allocations.add(new CommodityDiscountAllocation(commodity.getCommoditySkuId(), allocation));
                
                log.debug("工价优惠分摊，商品SKU: {}, 分摊金额: {}, 分摊比例: {}", 
                        commodity.getCommoditySkuId(), allocation, ratio);
            }
        }
        
        log.info("工价优惠分摊完成，分摊项数: {}", allocations.size());
        return allocations;
    }

    @Override
    public String getRuleDescription(ActivityInfo activity) {
        StringBuilder description = new StringBuilder("工价优惠活动：");
        
        if (activity.getLaborDiscountAmount() != null && activity.getLaborDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
            description.append("每件商品优惠").append(activity.getLaborDiscountAmount()).append("元工价");
        } else if (activity.getLaborDiscountRate() != null && activity.getLaborDiscountRate().compareTo(BigDecimal.ZERO) > 0) {
            description.append("工价优惠").append(activity.getLaborDiscountRate().multiply(new BigDecimal(100))).append("%");
        } else {
            description.append("工价优惠活动");
        }
        
        return description.toString();
    }
}
