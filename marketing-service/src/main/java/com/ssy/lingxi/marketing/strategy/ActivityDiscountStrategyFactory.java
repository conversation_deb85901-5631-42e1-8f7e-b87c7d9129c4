package com.ssy.lingxi.marketing.strategy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 营销活动折扣策略工厂
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-26
 */
@Slf4j
@Component
public class ActivityDiscountStrategyFactory {

    @Autowired
    private List<ActivityDiscountStrategy> strategies;

    private Map<Integer, ActivityDiscountStrategy> strategyMap = new HashMap<>();

    @PostConstruct
    public void init() {
        log.info("初始化营销活动折扣策略工厂，策略数量: {}", strategies.size());
        
        for (ActivityDiscountStrategy strategy : strategies) {
            Integer activityType = strategy.getSupportedActivityType();
            strategyMap.put(activityType, strategy);
            log.info("注册营销活动策略，活动类型: {}, 策略类: {}", activityType, strategy.getClass().getSimpleName());
        }
        
        log.info("营销活动折扣策略工厂初始化完成，支持的活动类型: {}", strategyMap.keySet());
    }

    /**
     * 根据活动类型获取对应的策略
     * 
     * @param activityType 活动类型
     * @return 对应的策略，如果不支持则返回null
     */
    public ActivityDiscountStrategy getStrategy(Integer activityType) {
        ActivityDiscountStrategy strategy = strategyMap.get(activityType);
        
        if (strategy == null) {
            log.warn("不支持的营销活动类型: {}", activityType);
        }
        
        return strategy;
    }

    /**
     * 检查是否支持指定的活动类型
     * 
     * @param activityType 活动类型
     * @return 是否支持
     */
    public boolean isSupported(Integer activityType) {
        return strategyMap.containsKey(activityType);
    }

    /**
     * 获取所有支持的活动类型
     *
     * @return 支持的活动类型列表
     */
    public List<Integer> getSupportedActivityTypes() {
        return strategyMap.keySet().stream().collect(java.util.stream.Collectors.toList());
    }
}
