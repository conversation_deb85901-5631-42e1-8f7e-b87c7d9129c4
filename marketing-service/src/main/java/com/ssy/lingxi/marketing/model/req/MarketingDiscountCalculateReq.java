package com.ssy.lingxi.marketing.model.req;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 营销活动折扣计算请求
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-26
 */
@Data
public class MarketingDiscountCalculateReq {

    /**
     * 店铺ID
     */
    @NotNull(message = "店铺ID不能为空")
    private Long shopId;

    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空")
    private Long memberId;

    /**
     * 会员角色ID
     */
    @NotNull(message = "会员角色ID不能为空")
    private Long memberRoleId;

    /**
     * 计算时间（用于判断活动有效期）
     */
    private LocalDateTime calculateTime = LocalDateTime.now();

    /**
     * 商品列表
     */
    @NotEmpty(message = "商品列表不能为空")
    @Valid
    private List<CommodityItem> commodityList;

    /**
     * 商品项
     */
    @Data
    public static class CommodityItem {
        
        /**
         * 商品SKU ID
         */
        @NotNull(message = "商品SKU ID不能为空")
        private Long commoditySkuId;

        /**
         * 商品ID
         */
        @NotNull(message = "商品ID不能为空")
        private Long commodityId;

        /**
         * 商品数量
         */
        @NotNull(message = "商品数量不能为空")
        private Integer quantity;

        /**
         * 商品单价
         */
        @NotNull(message = "商品单价不能为空")
        private BigDecimal unitPrice;

        /**
         * 商品总价（单价 * 数量）
         */
        @NotNull(message = "商品总价不能为空")
        private BigDecimal totalPrice;

        /**
         * 商品分类ID
         */
        private Long categoryId;

        /**
         * 商品名称
         */
        private String commodityName;

        /**
         * 商品重量（用于工价计算）
         */
        private BigDecimal weight;
    }
}
