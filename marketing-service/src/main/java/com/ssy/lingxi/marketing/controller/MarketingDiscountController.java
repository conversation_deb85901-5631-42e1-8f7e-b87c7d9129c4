package com.ssy.lingxi.marketing.controller;

import com.ssy.lingxi.component.base.model.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.req.MarketingDiscountCalculateReq;
import com.ssy.lingxi.marketing.model.resp.MarketingDiscountCalculateResp;
import com.ssy.lingxi.marketing.service.MarketingDiscountCalculateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 营销活动折扣计算控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-26
 */
@Slf4j
@RestController
@RequestMapping("/marketing/discount")
@Api(tags = "营销活动折扣计算")
public class MarketingDiscountController {

    @Autowired
    private MarketingDiscountCalculateService marketingDiscountCalculateService;

    /**
     * 计算营销活动折扣
     * 
     * @param request 计算请求
     * @return 计算结果
     */
    @PostMapping("/calculate")
    @ApiOperation("计算营销活动折扣")
    public WrapperResp<MarketingDiscountCalculateResp> calculateMarketingActivityDiscount(
            @RequestBody @Validated MarketingDiscountCalculateReq request) {
        
        log.info("接收到营销活动折扣计算请求，店铺ID: {}, 会员ID: {}, 商品数量: {}", 
                request.getShopId(), request.getMemberId(), request.getCommodityList().size());

        try {
            MarketingDiscountCalculateResp response = marketingDiscountCalculateService.calculateMarketingActivityDiscount(request);
            
            log.info("营销活动折扣计算完成，总优惠金额: {}", response.getTotalDiscount());
            return WrapperUtil.success(response);
            
        } catch (Exception e) {
            log.error("营销活动折扣计算异常", e);
            return WrapperUtil.error("营销活动折扣计算失败：" + e.getMessage());
        }
    }
}
