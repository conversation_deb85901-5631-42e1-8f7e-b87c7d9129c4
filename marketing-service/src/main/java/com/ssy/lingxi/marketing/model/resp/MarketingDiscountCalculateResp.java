package com.ssy.lingxi.marketing.model.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 营销活动折扣计算响应
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-26
 */
@Data
public class MarketingDiscountCalculateResp {

    /**
     * 总的活动优惠金额
     */
    private BigDecimal totalActivityDiscount = BigDecimal.ZERO;

    /**
     * 总的客户工费优惠金额
     */
    private BigDecimal totalLaborDiscount = BigDecimal.ZERO;

    /**
     * 总优惠金额（活动优惠 + 工费优惠）
     */
    private BigDecimal totalDiscount = BigDecimal.ZERO;

    /**
     * 商品级别的优惠详情
     */
    private List<CommodityDiscountDetail> commodityDiscountList;

    /**
     * 参与的活动详情
     */
    private List<ActivityDiscountDetail> activityDiscountList;

    /**
     * 商品优惠详情
     */
    @Data
    public static class CommodityDiscountDetail {
        
        /**
         * 商品SKU ID
         */
        private Long commoditySkuId;

        /**
         * 商品ID
         */
        private Long commodityId;

        /**
         * 活动优惠金额（分摊到该商品的活动优惠）
         */
        private BigDecimal activityDiscount = BigDecimal.ZERO;

        /**
         * 客户工费优惠金额
         */
        private BigDecimal laborDiscount = BigDecimal.ZERO;

        /**
         * 该商品总优惠金额
         */
        private BigDecimal totalDiscount = BigDecimal.ZERO;

        /**
         * 参与的活动ID
         */
        private Long participatedActivityId;

        /**
         * 参与的活动名称
         */
        private String participatedActivityName;

        /**
         * 参与的活动类型
         */
        private Integer participatedActivityType;
    }

    /**
     * 活动优惠详情
     */
    @Data
    public static class ActivityDiscountDetail {
        
        /**
         * 活动ID
         */
        private Long activityId;

        /**
         * 活动名称
         */
        private String activityName;

        /**
         * 活动类型（17-工价优惠，18-满额促销，19-满量促销）
         */
        private Integer activityType;

        /**
         * 活动类型名称
         */
        private String activityTypeName;

        /**
         * 参与该活动的商品SKU ID列表
         */
        private List<Long> participatedCommoditySkuIds;

        /**
         * 该活动的总优惠金额
         */
        private BigDecimal discountAmount = BigDecimal.ZERO;

        /**
         * 活动规则描述
         */
        private String ruleDescription;

        /**
         * 是否满足活动条件
         */
        private Boolean conditionMet;
    }
}
