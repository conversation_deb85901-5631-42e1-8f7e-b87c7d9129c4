package com.ssy.lingxi.marketing.strategy.impl;

import com.ssy.lingxi.marketing.model.req.MarketingDiscountCalculateReq;
import com.ssy.lingxi.marketing.strategy.ActivityDiscountStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 满量促销活动策略（活动类型19）
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-26
 */
@Slf4j
@Component
public class FullQuantityDiscountStrategy implements ActivityDiscountStrategy {

    private static final Integer ACTIVITY_TYPE = 19;

    @Override
    public Integer getSupportedActivityType() {
        return ACTIVITY_TYPE;
    }

    @Override
    public boolean checkCondition(ActivityInfo activity, List<MarketingDiscountCalculateReq.CommodityItem> commodityList) {
        log.info("检查满量促销活动条件，活动ID: {}, 商品数量: {}", activity.getActivityId(), commodityList.size());
        
        if (commodityList == null || commodityList.isEmpty()) {
            log.info("满量促销活动条件检查失败：商品列表为空，活动ID: {}", activity.getActivityId());
            return false;
        }
        
        if (activity.getFullQuantityThreshold() == null || activity.getFullQuantityThreshold() <= 0) {
            log.warn("满量促销活动配置错误：满量门槛未设置，活动ID: {}", activity.getActivityId());
            return false;
        }
        
        // 计算商品总数量
        int totalQuantity = commodityList.stream()
                .mapToInt(MarketingDiscountCalculateReq.CommodityItem::getQuantity)
                .sum();
        
        boolean conditionMet = totalQuantity >= activity.getFullQuantityThreshold();
        
        log.info("满量促销活动条件检查结果，活动ID: {}, 商品总数量: {}, 满量门槛: {}, 条件满足: {}", 
                activity.getActivityId(), totalQuantity, activity.getFullQuantityThreshold(), conditionMet);
        
        return conditionMet;
    }

    @Override
    public BigDecimal calculateDiscount(ActivityInfo activity, List<MarketingDiscountCalculateReq.CommodityItem> commodityList) {
        log.info("计算满量促销优惠，活动ID: {}, 商品数量: {}", activity.getActivityId(), commodityList.size());
        
        if (!checkCondition(activity, commodityList)) {
            log.info("满量促销活动条件不满足，无优惠，活动ID: {}", activity.getActivityId());
            return BigDecimal.ZERO;
        }
        
        int totalQuantity = commodityList.stream()
                .mapToInt(MarketingDiscountCalculateReq.CommodityItem::getQuantity)
                .sum();
        
        // 计算满量优惠次数（支持多倍满量）
        int discountTimes = totalQuantity / activity.getFullQuantityThreshold();
        BigDecimal totalDiscount = activity.getFullQuantityDiscount().multiply(new BigDecimal(discountTimes));
        
        log.info("满量促销优惠计算结果，活动ID: {}, 商品总数量: {}, 满量门槛: {}, 优惠次数: {}, 单次优惠: {}, 总优惠: {}", 
                activity.getActivityId(), totalQuantity, activity.getFullQuantityThreshold(), 
                discountTimes, activity.getFullQuantityDiscount(), totalDiscount);
        
        return totalDiscount;
    }

    @Override
    public List<CommodityDiscountAllocation> allocateDiscount(BigDecimal totalDiscount, List<MarketingDiscountCalculateReq.CommodityItem> commodityList) {
        log.info("分摊满量促销优惠，总优惠金额: {}, 商品数量: {}", totalDiscount, commodityList.size());
        
        List<CommodityDiscountAllocation> allocations = new ArrayList<>();
        
        if (totalDiscount.compareTo(BigDecimal.ZERO) <= 0 || commodityList.isEmpty()) {
            return allocations;
        }
        
        // 计算总数量
        int totalQuantity = commodityList.stream()
                .mapToInt(MarketingDiscountCalculateReq.CommodityItem::getQuantity)
                .sum();
        
        if (totalQuantity <= 0) {
            log.warn("商品总数量为0，无法分摊优惠");
            return allocations;
        }
        
        // 按商品数量比例分摊优惠
        BigDecimal allocatedTotal = BigDecimal.ZERO;
        
        for (int i = 0; i < commodityList.size(); i++) {
            MarketingDiscountCalculateReq.CommodityItem commodity = commodityList.get(i);
            BigDecimal allocation;
            
            if (i == commodityList.size() - 1) {
                // 最后一个商品，分摊剩余金额，避免精度问题
                allocation = totalDiscount.subtract(allocatedTotal);
            } else {
                // 按数量比例分摊
                BigDecimal ratio = new BigDecimal(commodity.getQuantity()).divide(new BigDecimal(totalQuantity), 6, RoundingMode.HALF_UP);
                allocation = totalDiscount.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
                allocatedTotal = allocatedTotal.add(allocation);
            }
            
            allocations.add(new CommodityDiscountAllocation(commodity.getCommoditySkuId(), allocation));
            
            log.debug("满量促销优惠分摊，商品SKU: {}, 商品数量: {}, 分摊金额: {}", 
                    commodity.getCommoditySkuId(), commodity.getQuantity(), allocation);
        }
        
        log.info("满量促销优惠分摊完成，分摊项数: {}", allocations.size());
        return allocations;
    }

    @Override
    public String getRuleDescription(ActivityInfo activity) {
        StringBuilder description = new StringBuilder("满量促销活动：");
        
        if (activity.getFullQuantityThreshold() != null && activity.getFullQuantityDiscount() != null) {
            description.append("满").append(activity.getFullQuantityThreshold()).append("件减").append(activity.getFullQuantityDiscount()).append("元");
        } else {
            description.append("满量促销活动");
        }
        
        return description.toString();
    }
}
