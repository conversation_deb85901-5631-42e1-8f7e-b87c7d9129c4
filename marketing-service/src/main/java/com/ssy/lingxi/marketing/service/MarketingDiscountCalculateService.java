package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.marketing.model.req.MarketingDiscountCalculateReq;
import com.ssy.lingxi.marketing.model.resp.MarketingDiscountCalculateResp;
import com.ssy.lingxi.marketing.strategy.ActivityDiscountStrategy;
import com.ssy.lingxi.marketing.strategy.ActivityDiscountStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 营销活动折扣计算服务
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-26
 */
@Slf4j
@Service
public class MarketingDiscountCalculateService {

    @Autowired
    private ActivityDiscountStrategyFactory strategyFactory;

    // 这里需要注入实际的活动查询服务和客户工费计算服务
    // @Autowired
    // private ActivityQueryService activityQueryService;
    // @Autowired
    // private CustomerLaborFeeService customerLaborFeeService;

    /**
     * 计算营销活动折扣
     * 
     * @param request 计算请求
     * @return 计算结果
     */
    public MarketingDiscountCalculateResp calculateMarketingActivityDiscount(MarketingDiscountCalculateReq request) {
        log.info("开始计算营销活动折扣，店铺ID: {}, 会员ID: {}, 商品数量: {}", 
                request.getShopId(), request.getMemberId(), request.getCommodityList().size());

        MarketingDiscountCalculateResp response = new MarketingDiscountCalculateResp();
        response.setCommodityDiscountList(new ArrayList<>());
        response.setActivityDiscountList(new ArrayList<>());

        try {
            // 第一步：查询可参与的活动
            List<ActivityDiscountStrategy.ActivityInfo> availableActivities = queryAvailableActivities(request);
            log.info("查询到可参与的活动数量: {}", availableActivities.size());

            // 第二步：根据活动关联的商品进行分组
            Map<Long, List<MarketingDiscountCalculateReq.CommodityItem>> activityCommodityGroups = 
                    groupCommoditiesByActivity(request.getCommodityList(), availableActivities);
            log.info("活动商品分组完成，分组数量: {}", activityCommodityGroups.size());

            // 第三步：为每个商品初始化优惠详情
            initializeCommodityDiscountDetails(response, request.getCommodityList());

            // 第四步：计算活动优惠
            calculateActivityDiscounts(response, activityCommodityGroups, availableActivities);

            // 第五步：计算客户工费优惠
            calculateCustomerLaborDiscounts(response, request);

            // 第六步：汇总计算结果
            summarizeDiscountResults(response);

            log.info("营销活动折扣计算完成，总活动优惠: {}, 总工费优惠: {}, 总优惠: {}", 
                    response.getTotalActivityDiscount(), response.getTotalLaborDiscount(), response.getTotalDiscount());

        } catch (Exception e) {
            log.error("营销活动折扣计算异常", e);
            // 异常情况下返回无优惠的结果
            initializeCommodityDiscountDetails(response, request.getCommodityList());
        }

        return response;
    }

    /**
     * 查询可参与的活动
     */
    private List<ActivityDiscountStrategy.ActivityInfo> queryAvailableActivities(MarketingDiscountCalculateReq request) {
        log.info("查询可参与的活动，店铺ID: {}, 会员ID: {}, 计算时间: {}", 
                request.getShopId(), request.getMemberId(), request.getCalculateTime());

        // TODO: 实际实现中需要调用活动查询服务
        // 这里模拟返回一些活动数据
        List<ActivityDiscountStrategy.ActivityInfo> activities = new ArrayList<>();
        
        // 支持的活动类型：17-工价优惠，18-满额促销，19-满量促销
        List<Integer> supportedTypes = Arrays.asList(17, 18, 19);
        
        // 根据时间、店铺、会员等条件查询活动
        // activities = activityQueryService.queryAvailableActivities(request.getShopId(), 
        //         request.getMemberId(), request.getMemberRoleId(), request.getCalculateTime(), supportedTypes);
        
        log.info("查询到可参与的活动数量: {}", activities.size());
        return activities;
    }

    /**
     * 根据活动关联的商品进行分组
     * 每个商品只允许参加一个活动
     */
    private Map<Long, List<MarketingDiscountCalculateReq.CommodityItem>> groupCommoditiesByActivity(
            List<MarketingDiscountCalculateReq.CommodityItem> commodityList,
            List<ActivityDiscountStrategy.ActivityInfo> availableActivities) {
        
        log.info("开始进行活动商品分组，商品数量: {}, 活动数量: {}", commodityList.size(), availableActivities.size());
        
        Map<Long, List<MarketingDiscountCalculateReq.CommodityItem>> activityGroups = new HashMap<>();
        Set<Long> assignedCommodities = new HashSet<>();

        // 按活动优先级分配商品（这里简化处理，实际可能需要更复杂的优先级逻辑）
        for (ActivityDiscountStrategy.ActivityInfo activity : availableActivities) {
            List<MarketingDiscountCalculateReq.CommodityItem> activityCommodities = new ArrayList<>();
            
            for (MarketingDiscountCalculateReq.CommodityItem commodity : commodityList) {
                // 如果商品已经分配给其他活动，跳过
                if (assignedCommodities.contains(commodity.getCommoditySkuId())) {
                    continue;
                }
                
                // 检查商品是否符合该活动的参与条件
                if (isCommodityEligibleForActivity(commodity, activity)) {
                    activityCommodities.add(commodity);
                    assignedCommodities.add(commodity.getCommoditySkuId());
                }
            }
            
            if (!activityCommodities.isEmpty()) {
                activityGroups.put(activity.getActivityId(), activityCommodities);
                log.info("活动分组完成，活动ID: {}, 活动类型: {}, 商品数量: {}", 
                        activity.getActivityId(), activity.getActivityType(), activityCommodities.size());
            }
        }

        log.info("活动商品分组完成，已分配商品数量: {}, 未分配商品数量: {}", 
                assignedCommodities.size(), commodityList.size() - assignedCommodities.size());
        
        return activityGroups;
    }

    /**
     * 检查商品是否符合活动的参与条件
     */
    private boolean isCommodityEligibleForActivity(MarketingDiscountCalculateReq.CommodityItem commodity, 
                                                   ActivityDiscountStrategy.ActivityInfo activity) {
        // TODO: 实际实现中需要根据活动配置的商品范围进行判断
        // 这里简化处理，假设所有商品都可以参与所有活动
        return true;
    }

    /**
     * 初始化商品优惠详情
     */
    private void initializeCommodityDiscountDetails(MarketingDiscountCalculateResp response, 
                                                   List<MarketingDiscountCalculateReq.CommodityItem> commodityList) {
        List<MarketingDiscountCalculateResp.CommodityDiscountDetail> commodityDiscountList = new ArrayList<>();
        
        for (MarketingDiscountCalculateReq.CommodityItem commodity : commodityList) {
            MarketingDiscountCalculateResp.CommodityDiscountDetail detail = 
                    new MarketingDiscountCalculateResp.CommodityDiscountDetail();
            detail.setCommoditySkuId(commodity.getCommoditySkuId());
            detail.setCommodityId(commodity.getCommodityId());
            detail.setActivityDiscount(BigDecimal.ZERO);
            detail.setLaborDiscount(BigDecimal.ZERO);
            detail.setTotalDiscount(BigDecimal.ZERO);
            
            commodityDiscountList.add(detail);
        }
        
        response.setCommodityDiscountList(commodityDiscountList);
    }

    /**
     * 计算活动优惠
     */
    private void calculateActivityDiscounts(MarketingDiscountCalculateResp response,
                                          Map<Long, List<MarketingDiscountCalculateReq.CommodityItem>> activityCommodityGroups,
                                          List<ActivityDiscountStrategy.ActivityInfo> availableActivities) {

        log.info("开始计算活动优惠，活动分组数量: {}", activityCommodityGroups.size());

        Map<Long, ActivityDiscountStrategy.ActivityInfo> activityMap = availableActivities.stream()
                .collect(Collectors.toMap(ActivityDiscountStrategy.ActivityInfo::getActivityId, activity -> activity));

        for (Map.Entry<Long, List<MarketingDiscountCalculateReq.CommodityItem>> entry : activityCommodityGroups.entrySet()) {
            Long activityId = entry.getKey();
            List<MarketingDiscountCalculateReq.CommodityItem> commodities = entry.getValue();
            ActivityDiscountStrategy.ActivityInfo activity = activityMap.get(activityId);

            if (activity == null) {
                log.warn("活动信息不存在，活动ID: {}", activityId);
                continue;
            }

            // 获取对应的策略
            ActivityDiscountStrategy strategy = strategyFactory.getStrategy(activity.getActivityType());
            if (strategy == null) {
                log.warn("不支持的活动类型，活动ID: {}, 活动类型: {}", activityId, activity.getActivityType());
                continue;
            }

            try {
                // 检查活动条件
                boolean conditionMet = strategy.checkCondition(activity, commodities);

                // 计算活动优惠
                BigDecimal activityDiscount = BigDecimal.ZERO;
                if (conditionMet) {
                    activityDiscount = strategy.calculateDiscount(activity, commodities);
                }

                // 分摊优惠到各个商品
                if (activityDiscount.compareTo(BigDecimal.ZERO) > 0) {
                    List<ActivityDiscountStrategy.CommodityDiscountAllocation> allocations =
                            strategy.allocateDiscount(activityDiscount, commodities);
                    applyActivityDiscountAllocations(response, allocations, activity);
                }

                // 记录活动优惠详情
                MarketingDiscountCalculateResp.ActivityDiscountDetail activityDetail =
                        new MarketingDiscountCalculateResp.ActivityDiscountDetail();
                activityDetail.setActivityId(activity.getActivityId());
                activityDetail.setActivityName(activity.getActivityName());
                activityDetail.setActivityType(activity.getActivityType());
                activityDetail.setActivityTypeName(activity.getActivityTypeName());
                activityDetail.setParticipatedCommoditySkuIds(
                        commodities.stream().map(MarketingDiscountCalculateReq.CommodityItem::getCommoditySkuId)
                                .collect(Collectors.toList()));
                activityDetail.setDiscountAmount(activityDiscount);
                activityDetail.setRuleDescription(strategy.getRuleDescription(activity));
                activityDetail.setConditionMet(conditionMet);

                response.getActivityDiscountList().add(activityDetail);

                log.info("活动优惠计算完成，活动ID: {}, 活动类型: {}, 优惠金额: {}, 条件满足: {}",
                        activityId, activity.getActivityType(), activityDiscount, conditionMet);

            } catch (Exception e) {
                log.error("活动优惠计算异常，活动ID: {}", activityId, e);
            }
        }
    }

    /**
     * 应用活动优惠分摊结果
     */
    private void applyActivityDiscountAllocations(MarketingDiscountCalculateResp response,
                                                List<ActivityDiscountStrategy.CommodityDiscountAllocation> allocations,
                                                ActivityDiscountStrategy.ActivityInfo activity) {

        Map<Long, MarketingDiscountCalculateResp.CommodityDiscountDetail> commodityDetailMap =
                response.getCommodityDiscountList().stream()
                        .collect(Collectors.toMap(MarketingDiscountCalculateResp.CommodityDiscountDetail::getCommoditySkuId,
                                detail -> detail));

        for (ActivityDiscountStrategy.CommodityDiscountAllocation allocation : allocations) {
            MarketingDiscountCalculateResp.CommodityDiscountDetail detail =
                    commodityDetailMap.get(allocation.getCommoditySkuId());

            if (detail != null) {
                detail.setActivityDiscount(detail.getActivityDiscount().add(allocation.getDiscountAmount()));
                detail.setParticipatedActivityId(activity.getActivityId());
                detail.setParticipatedActivityName(activity.getActivityName());
                detail.setParticipatedActivityType(activity.getActivityType());

                log.debug("应用活动优惠分摊，商品SKU: {}, 分摊金额: {}",
                        allocation.getCommoditySkuId(), allocation.getDiscountAmount());
            }
        }
    }

    /**
     * 计算客户工费优惠
     */
    private void calculateCustomerLaborDiscounts(MarketingDiscountCalculateResp response,
                                               MarketingDiscountCalculateReq request) {
        log.info("开始计算客户工费优惠，商品数量: {}", request.getCommodityList().size());

        for (MarketingDiscountCalculateReq.CommodityItem commodity : request.getCommodityList()) {
            try {
                // TODO: 调用客户工费计算接口
                BigDecimal laborDiscount = calculateSingleCommodityLaborDiscount(commodity, request);

                // 应用工费优惠到商品详情
                MarketingDiscountCalculateResp.CommodityDiscountDetail detail =
                        response.getCommodityDiscountList().stream()
                                .filter(d -> d.getCommoditySkuId().equals(commodity.getCommoditySkuId()))
                                .findFirst()
                                .orElse(null);

                if (detail != null) {
                    detail.setLaborDiscount(laborDiscount);
                    log.debug("应用客户工费优惠，商品SKU: {}, 工费优惠: {}",
                            commodity.getCommoditySkuId(), laborDiscount);
                }

            } catch (Exception e) {
                log.error("计算客户工费优惠异常，商品SKU: {}", commodity.getCommoditySkuId(), e);
            }
        }
    }

    /**
     * 计算单个商品的客户工费优惠
     */
    private BigDecimal calculateSingleCommodityLaborDiscount(MarketingDiscountCalculateReq.CommodityItem commodity,
                                                           MarketingDiscountCalculateReq request) {
        // TODO: 实际实现中需要调用客户工费计算接口
        // return customerLaborFeeService.calculateLaborDiscount(request.getMemberId(),
        //         request.getMemberRoleId(), commodity);

        // 这里模拟返回工费优惠
        return BigDecimal.ZERO;
    }

    /**
     * 汇总计算结果
     */
    private void summarizeDiscountResults(MarketingDiscountCalculateResp response) {
        BigDecimal totalActivityDiscount = BigDecimal.ZERO;
        BigDecimal totalLaborDiscount = BigDecimal.ZERO;

        for (MarketingDiscountCalculateResp.CommodityDiscountDetail detail : response.getCommodityDiscountList()) {
            // 计算商品总优惠
            BigDecimal commodityTotalDiscount = detail.getActivityDiscount().add(detail.getLaborDiscount());
            detail.setTotalDiscount(commodityTotalDiscount);

            // 累计总优惠
            totalActivityDiscount = totalActivityDiscount.add(detail.getActivityDiscount());
            totalLaborDiscount = totalLaborDiscount.add(detail.getLaborDiscount());
        }

        response.setTotalActivityDiscount(totalActivityDiscount);
        response.setTotalLaborDiscount(totalLaborDiscount);
        response.setTotalDiscount(totalActivityDiscount.add(totalLaborDiscount));

        log.info("优惠汇总完成，总活动优惠: {}, 总工费优惠: {}, 总优惠: {}",
                totalActivityDiscount, totalLaborDiscount, response.getTotalDiscount());
    }
}
