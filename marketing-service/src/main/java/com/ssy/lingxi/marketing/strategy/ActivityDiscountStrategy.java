package com.ssy.lingxi.marketing.strategy;

import com.ssy.lingxi.marketing.model.req.MarketingDiscountCalculateReq;
import com.ssy.lingxi.marketing.model.resp.MarketingDiscountCalculateResp;

import java.math.BigDecimal;
import java.util.List;

/**
 * 营销活动折扣计算策略接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-26
 */
public interface ActivityDiscountStrategy {

    /**
     * 获取支持的活动类型
     * 
     * @return 活动类型代码
     */
    Integer getSupportedActivityType();

    /**
     * 检查是否满足活动条件
     * 
     * @param activity 活动信息
     * @param commodityList 参与该活动的商品列表
     * @return 是否满足条件
     */
    boolean checkCondition(ActivityInfo activity, List<MarketingDiscountCalculateReq.CommodityItem> commodityList);

    /**
     * 计算活动优惠金额
     * 
     * @param activity 活动信息
     * @param commodityList 参与该活动的商品列表
     * @return 优惠金额
     */
    BigDecimal calculateDiscount(ActivityInfo activity, List<MarketingDiscountCalculateReq.CommodityItem> commodityList);

    /**
     * 将活动优惠金额分摊到各个商品
     * 
     * @param totalDiscount 总优惠金额
     * @param commodityList 参与该活动的商品列表
     * @return 分摊结果，key为commoditySkuId，value为分摊的优惠金额
     */
    List<CommodityDiscountAllocation> allocateDiscount(BigDecimal totalDiscount, List<MarketingDiscountCalculateReq.CommodityItem> commodityList);

    /**
     * 获取活动规则描述
     * 
     * @param activity 活动信息
     * @return 规则描述
     */
    String getRuleDescription(ActivityInfo activity);

    /**
     * 活动信息
     */
    class ActivityInfo {
        private Long activityId;
        private String activityName;
        private Integer activityType;
        private String activityTypeName;
        
        // 工价优惠活动专用字段
        private BigDecimal laborDiscountRate; // 工价优惠比例
        private BigDecimal laborDiscountAmount; // 工价优惠金额
        
        // 满额促销活动专用字段
        private BigDecimal fullAmountThreshold; // 满额门槛
        private BigDecimal fullAmountDiscount; // 满额优惠金额
        
        // 满量促销活动专用字段
        private Integer fullQuantityThreshold; // 满量门槛
        private BigDecimal fullQuantityDiscount; // 满量优惠金额

        // 构造函数、getter、setter省略...
        public ActivityInfo() {}

        public ActivityInfo(Long activityId, String activityName, Integer activityType, String activityTypeName) {
            this.activityId = activityId;
            this.activityName = activityName;
            this.activityType = activityType;
            this.activityTypeName = activityTypeName;
        }

        // Getters and Setters
        public Long getActivityId() { return activityId; }
        public void setActivityId(Long activityId) { this.activityId = activityId; }
        
        public String getActivityName() { return activityName; }
        public void setActivityName(String activityName) { this.activityName = activityName; }
        
        public Integer getActivityType() { return activityType; }
        public void setActivityType(Integer activityType) { this.activityType = activityType; }
        
        public String getActivityTypeName() { return activityTypeName; }
        public void setActivityTypeName(String activityTypeName) { this.activityTypeName = activityTypeName; }
        
        public BigDecimal getLaborDiscountRate() { return laborDiscountRate; }
        public void setLaborDiscountRate(BigDecimal laborDiscountRate) { this.laborDiscountRate = laborDiscountRate; }
        
        public BigDecimal getLaborDiscountAmount() { return laborDiscountAmount; }
        public void setLaborDiscountAmount(BigDecimal laborDiscountAmount) { this.laborDiscountAmount = laborDiscountAmount; }
        
        public BigDecimal getFullAmountThreshold() { return fullAmountThreshold; }
        public void setFullAmountThreshold(BigDecimal fullAmountThreshold) { this.fullAmountThreshold = fullAmountThreshold; }
        
        public BigDecimal getFullAmountDiscount() { return fullAmountDiscount; }
        public void setFullAmountDiscount(BigDecimal fullAmountDiscount) { this.fullAmountDiscount = fullAmountDiscount; }
        
        public Integer getFullQuantityThreshold() { return fullQuantityThreshold; }
        public void setFullQuantityThreshold(Integer fullQuantityThreshold) { this.fullQuantityThreshold = fullQuantityThreshold; }
        
        public BigDecimal getFullQuantityDiscount() { return fullQuantityDiscount; }
        public void setFullQuantityDiscount(BigDecimal fullQuantityDiscount) { this.fullQuantityDiscount = fullQuantityDiscount; }
    }

    /**
     * 商品优惠分摊结果
     */
    class CommodityDiscountAllocation {
        private Long commoditySkuId;
        private BigDecimal discountAmount;

        public CommodityDiscountAllocation() {}

        public CommodityDiscountAllocation(Long commoditySkuId, BigDecimal discountAmount) {
            this.commoditySkuId = commoditySkuId;
            this.discountAmount = discountAmount;
        }

        // Getters and Setters
        public Long getCommoditySkuId() { return commoditySkuId; }
        public void setCommoditySkuId(Long commoditySkuId) { this.commoditySkuId = commoditySkuId; }
        
        public BigDecimal getDiscountAmount() { return discountAmount; }
        public void setDiscountAmount(BigDecimal discountAmount) { this.discountAmount = discountAmount; }
    }
}
