package com.ssy.lingxi.marketing.strategy.impl;

import com.ssy.lingxi.marketing.model.req.MarketingDiscountCalculateReq;
import com.ssy.lingxi.marketing.strategy.ActivityDiscountStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 满额促销活动策略（活动类型18）
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-26
 */
@Slf4j
@Component
public class FullAmountDiscountStrategy implements ActivityDiscountStrategy {

    private static final Integer ACTIVITY_TYPE = 18;

    @Override
    public Integer getSupportedActivityType() {
        return ACTIVITY_TYPE;
    }

    @Override
    public boolean checkCondition(ActivityInfo activity, List<MarketingDiscountCalculateReq.CommodityItem> commodityList) {
        log.info("检查满额促销活动条件，活动ID: {}, 商品数量: {}", activity.getActivityId(), commodityList.size());
        
        if (commodityList == null || commodityList.isEmpty()) {
            log.info("满额促销活动条件检查失败：商品列表为空，活动ID: {}", activity.getActivityId());
            return false;
        }
        
        if (activity.getFullAmountThreshold() == null || activity.getFullAmountThreshold().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("满额促销活动配置错误：满额门槛未设置，活动ID: {}", activity.getActivityId());
            return false;
        }
        
        // 计算商品总金额
        BigDecimal totalAmount = commodityList.stream()
                .map(MarketingDiscountCalculateReq.CommodityItem::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        boolean conditionMet = totalAmount.compareTo(activity.getFullAmountThreshold()) >= 0;
        
        log.info("满额促销活动条件检查结果，活动ID: {}, 商品总金额: {}, 满额门槛: {}, 条件满足: {}", 
                activity.getActivityId(), totalAmount, activity.getFullAmountThreshold(), conditionMet);
        
        return conditionMet;
    }

    @Override
    public BigDecimal calculateDiscount(ActivityInfo activity, List<MarketingDiscountCalculateReq.CommodityItem> commodityList) {
        log.info("计算满额促销优惠，活动ID: {}, 商品数量: {}", activity.getActivityId(), commodityList.size());
        
        if (!checkCondition(activity, commodityList)) {
            log.info("满额促销活动条件不满足，无优惠，活动ID: {}", activity.getActivityId());
            return BigDecimal.ZERO;
        }
        
        BigDecimal totalAmount = commodityList.stream()
                .map(MarketingDiscountCalculateReq.CommodityItem::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 计算满额优惠次数（支持多倍满额）
        BigDecimal discountTimes = totalAmount.divide(activity.getFullAmountThreshold(), 0, RoundingMode.DOWN);
        BigDecimal totalDiscount = activity.getFullAmountDiscount().multiply(discountTimes);
        
        log.info("满额促销优惠计算结果，活动ID: {}, 商品总金额: {}, 满额门槛: {}, 优惠次数: {}, 单次优惠: {}, 总优惠: {}", 
                activity.getActivityId(), totalAmount, activity.getFullAmountThreshold(), 
                discountTimes, activity.getFullAmountDiscount(), totalDiscount);
        
        return totalDiscount;
    }

    @Override
    public List<CommodityDiscountAllocation> allocateDiscount(BigDecimal totalDiscount, List<MarketingDiscountCalculateReq.CommodityItem> commodityList) {
        log.info("分摊满额促销优惠，总优惠金额: {}, 商品数量: {}", totalDiscount, commodityList.size());
        
        List<CommodityDiscountAllocation> allocations = new ArrayList<>();
        
        if (totalDiscount.compareTo(BigDecimal.ZERO) <= 0 || commodityList.isEmpty()) {
            return allocations;
        }
        
        // 计算总金额
        BigDecimal totalAmount = commodityList.stream()
                .map(MarketingDiscountCalculateReq.CommodityItem::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        if (totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("商品总金额为0，无法分摊优惠");
            return allocations;
        }
        
        // 按商品金额比例分摊优惠
        BigDecimal allocatedTotal = BigDecimal.ZERO;
        
        for (int i = 0; i < commodityList.size(); i++) {
            MarketingDiscountCalculateReq.CommodityItem commodity = commodityList.get(i);
            BigDecimal allocation;
            
            if (i == commodityList.size() - 1) {
                // 最后一个商品，分摊剩余金额，避免精度问题
                allocation = totalDiscount.subtract(allocatedTotal);
            } else {
                // 按比例分摊
                BigDecimal ratio = commodity.getTotalPrice().divide(totalAmount, 6, RoundingMode.HALF_UP);
                allocation = totalDiscount.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
                allocatedTotal = allocatedTotal.add(allocation);
            }
            
            allocations.add(new CommodityDiscountAllocation(commodity.getCommoditySkuId(), allocation));
            
            log.debug("满额促销优惠分摊，商品SKU: {}, 商品金额: {}, 分摊金额: {}", 
                    commodity.getCommoditySkuId(), commodity.getTotalPrice(), allocation);
        }
        
        log.info("满额促销优惠分摊完成，分摊项数: {}", allocations.size());
        return allocations;
    }

    @Override
    public String getRuleDescription(ActivityInfo activity) {
        StringBuilder description = new StringBuilder("满额促销活动：");
        
        if (activity.getFullAmountThreshold() != null && activity.getFullAmountDiscount() != null) {
            description.append("满").append(activity.getFullAmountThreshold()).append("元减").append(activity.getFullAmountDiscount()).append("元");
        } else {
            description.append("满额促销活动");
        }
        
        return description.toString();
    }
}
