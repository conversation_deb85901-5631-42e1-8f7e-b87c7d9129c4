package com.ssy.lingxi.member.handler.validator;

import com.ssy.lingxi.component.base.enums.PurchaseContractPayTypeEnum;
import com.ssy.lingxi.member.handler.annotation.PurchaseContractPayTypeAnnotation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;

/**
 * 采购合同付款方式校验注解验证类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-16
 */
public class PurchaseContractPayTypeValidator implements ConstraintValidator<PurchaseContractPayTypeAnnotation, Integer> {
    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if(value == null) {
            return false;
        }

        return Arrays.stream(PurchaseContractPayTypeEnum.values()).map(PurchaseContractPayTypeEnum::getCode).anyMatch(v -> v.equals(value));
    }
}
