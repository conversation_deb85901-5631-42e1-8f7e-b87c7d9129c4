package com.ssy.lingxi.member.handler.validator;

import com.ssy.lingxi.member.api.enums.MemberRightAcquireWayEnum;
import com.ssy.lingxi.member.handler.annotation.MemberRightAcquireWayAnno;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;

/**
 * 会员权益获取方式注解校验类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-07
 */
public class MemberRightAcquireWayValidator implements ConstraintValidator<MemberRightAcquireWayAnno, Integer> {
    private boolean required = false;
    @Override
    public void initialize(MemberRightAcquireWayAnno constraintAnnotation) {
        required = constraintAnnotation.required();
    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if(!required) {
            return true;
        }

        if(value == null) {
            return false;
        }
        return Arrays.stream(MemberRightAcquireWayEnum.values()).anyMatch(c -> c.getCode().equals(value));
    }
}
