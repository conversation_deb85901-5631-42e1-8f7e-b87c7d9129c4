# 营销活动折扣计算系统设计文档

## 1. 系统概述

### 1.1 业务背景
营销活动折扣计算系统负责计算用户在购买商品时可以享受的各种优惠，包括营销活动优惠和客户工费优惠。

### 1.2 支持的活动类型
- **17 - 工价优惠活动**：用来计算出工价优惠多少
- **18 - 满额促销活动**：金额满了多少减多少
- **19 - 满量促销活动**：满多少件减多少

### 1.3 核心原则
- 每个商品只允许参加一个营销活动
- 营销活动优惠和客户工费优惠可以同时享受
- 支持活动优惠的精确分摊到商品级别

## 2. 系统架构

### 2.1 整体架构
```
Controller Layer (控制器层)
    ↓
Service Layer (服务层)
    ↓
Strategy Layer (策略层)
    ↓
Repository Layer (数据访问层)
```

### 2.2 核心组件

#### 2.2.1 策略模式设计
- **ActivityDiscountStrategy**: 活动折扣策略接口
- **LaborDiscountStrategy**: 工价优惠策略实现（类型17）
- **FullAmountDiscountStrategy**: 满额促销策略实现（类型18）
- **FullQuantityDiscountStrategy**: 满量促销策略实现（类型19）
- **ActivityDiscountStrategyFactory**: 策略工厂

#### 2.2.2 服务层
- **MarketingDiscountCalculateService**: 主要的折扣计算服务

#### 2.2.3 控制器层
- **MarketingDiscountController**: 对外提供API接口

## 3. 业务流程

### 3.1 主要流程
```
1. 接收计算请求
    ↓
2. 查询可参与的活动
    ↓
3. 根据活动关联的商品进行分组
    ↓
4. 初始化商品优惠详情
    ↓
5. 计算活动优惠
    ↓
6. 计算客户工费优惠
    ↓
7. 汇总计算结果
    ↓
8. 返回计算结果
```

### 3.2 活动匹配逻辑
1. **基础条件过滤**：根据时间、店铺、会员等基础字段筛选可参与的活动
2. **商品分组**：根据活动关联的商品对入参商品进行分组
3. **条件检查**：检查分组后的商品是否满足活动规则
4. **优惠计算**：满足条件的活动计算优惠金额
5. **优惠分摊**：将活动优惠均摊到每个商品上

### 3.3 优惠分摊策略
- **工价优惠（17）**：按商品实际工价计算，或按商品总价比例分摊
- **满额促销（18）**：按商品金额比例分摊
- **满量促销（19）**：按商品数量比例分摊

## 4. 接口设计

### 4.1 计算请求接口
```http
POST /marketing/discount/calculate
```

#### 请求参数
```json
{
  "shopId": 123,
  "memberId": 456,
  "memberRoleId": 789,
  "calculateTime": "2025-08-26T10:00:00",
  "commodityList": [
    {
      "commoditySkuId": 1001,
      "commodityId": 2001,
      "quantity": 2,
      "unitPrice": 100.00,
      "totalPrice": 200.00,
      "categoryId": 3001,
      "commodityName": "黄金戒指",
      "weight": 5.5
    }
  ]
}
```

#### 响应结果
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalActivityDiscount": 50.00,
    "totalLaborDiscount": 20.00,
    "totalDiscount": 70.00,
    "commodityDiscountList": [
      {
        "commoditySkuId": 1001,
        "commodityId": 2001,
        "activityDiscount": 50.00,
        "laborDiscount": 20.00,
        "totalDiscount": 70.00,
        "participatedActivityId": 5001,
        "participatedActivityName": "满额促销活动",
        "participatedActivityType": 18
      }
    ],
    "activityDiscountList": [
      {
        "activityId": 5001,
        "activityName": "满额促销活动",
        "activityType": 18,
        "activityTypeName": "满额促销",
        "participatedCommoditySkuIds": [1001],
        "discountAmount": 50.00,
        "ruleDescription": "满额促销活动：满200元减50元",
        "conditionMet": true
      }
    ]
  }
}
```

## 5. 数据模型

### 5.1 请求模型
- **MarketingDiscountCalculateReq**: 折扣计算请求
- **CommodityItem**: 商品项信息

### 5.2 响应模型
- **MarketingDiscountCalculateResp**: 折扣计算响应
- **CommodityDiscountDetail**: 商品级别优惠详情
- **ActivityDiscountDetail**: 活动级别优惠详情

### 5.3 策略模型
- **ActivityInfo**: 活动信息
- **CommodityDiscountAllocation**: 商品优惠分摊结果

## 6. 扩展性设计

### 6.1 新增活动类型
1. 实现 `ActivityDiscountStrategy` 接口
2. 添加 `@Component` 注解，自动注册到策略工厂
3. 实现具体的条件检查、优惠计算、分摊逻辑

### 6.2 自定义分摊策略
每种活动类型可以实现自己的分摊逻辑：
- 按金额比例分摊
- 按数量比例分摊
- 按重量比例分摊
- 固定金额分摊

### 6.3 优先级控制
支持活动优先级配置，当商品可以参与多个活动时，选择优先级最高的活动。

## 7. 性能优化

### 7.1 批量查询
- 批量查询活动信息
- 批量查询商品关联关系
- 批量计算客户工费优惠

### 7.2 缓存策略
- 活动信息缓存
- 商品分类关系缓存
- 客户工费配置缓存

### 7.3 异步处理
对于非实时性要求的计算，可以考虑异步处理提升响应速度。

## 8. 监控与日志

### 8.1 关键日志
- 活动查询日志
- 商品分组日志
- 优惠计算日志
- 分摊结果日志
- 异常处理日志

### 8.2 监控指标
- 计算请求量
- 计算成功率
- 平均计算耗时
- 活动参与率
- 优惠金额分布

## 9. 测试策略

### 9.1 单元测试
- 各策略类的单元测试
- 服务类的单元测试
- 工具类的单元测试

### 9.2 集成测试
- 完整流程的集成测试
- 各种活动类型的测试
- 边界条件测试

### 9.3 性能测试
- 大量商品的计算性能测试
- 并发请求的性能测试
- 内存使用情况测试

## 10. 部署与运维

### 10.1 配置管理
- 活动类型配置
- 分摊策略配置
- 缓存配置
- 日志级别配置

### 10.2 故障处理
- 计算异常时的降级策略
- 外部服务不可用时的处理
- 数据不一致时的处理

### 10.3 版本升级
- 向后兼容性保证
- 灰度发布策略
- 回滚方案
